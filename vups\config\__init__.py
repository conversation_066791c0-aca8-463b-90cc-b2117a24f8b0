import json
import pathlib

VUPS_PROJECT_ROOT = pathlib.Path(__file__).parent.parent.resolve()
CONFIG_ROOT = pathlib.Path(__file__).parent.resolve()
VUPS_CONFIG_PATH = CONFIG_ROOT / "vups.json"

def read_vups_config(vup_dict: dict = None):
    """
    Read VUP configuration from vups.json file

    Returns:
        list: List of VUP configuration dictionaries
    """
    # TODO: User Custom Dict
    if vup_dict:
        return vup_dict

    with open(VUPS_CONFIG_PATH, "r", encoding="utf-8") as file:
        return json.load(file)
