exist_table_sql = """SELECT to_regclass($1) IS NOT NULL AS table_exists;""" # Changed %s to $1
delete_table_sql = """DROP table IF EXISTS $1;""" # Changed %s to $1

# --- current_stat_table ---
create_current_stat_table_sql = """CREATE TABLE IF NOT EXISTS current_stat_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    name varchar(100),
    timestamp bigint,
    datetime timestamp,
    follower_num bigint,
    dahanghai_num bigint,
    video_total_num bigint,
    article_total_num bigint,
    likes_total_num bigint,
    elec_num bigint,
    UNIQUE (uid, timestamp) -- Add unique constraint for ON CONFLICT clause
);"""

insert_current_stat_table_sql = """INSERT INTO current_stat_table (
    uid, name, timestamp, datetime, follower_num, dahanghai_num, video_total_num, article_total_num, likes_total_num, elec_num
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
) ON CONFLICT (uid, timestamp) DO UPDATE SET
    name = EXCLUDED.name,
    datetime = EXCLUDED.datetime,
    follower_num = EXCLUDED.follower_num,
    dahanghai_num = EXCLUDED.dahanghai_num,
    video_total_num = EXCLUDED.video_total_num,
    article_total_num = EXCLUDED.article_total_num,
    likes_total_num = EXCLUDED.likes_total_num,
    elec_num = EXCLUDED.elec_num;
"""

# --- dynamics_table ---
create_dynamics_table_sql = """CREATE TABLE IF NOT EXISTS dynamics_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    name text,
    timestamp bigint,
    datetime timestamp,
    dynamic_content text,
    url text,
    topic text,
    dynamic_id text UNIQUE,
    share_num bigint,
    comment_num bigint,
    like_num bigint,
    comment_id text,
    comment_type int,
    heat float
);"""

insert_dynamics_table_sql = """INSERT INTO dynamics_table (
    uid, name, timestamp, datetime, dynamic_content, url, topic, dynamic_id, share_num, comment_num, like_num, comment_id, comment_type, heat
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
) ON CONFLICT (dynamic_id) DO UPDATE SET
    name = EXCLUDED.name,
    timestamp = EXCLUDED.timestamp,
    datetime = EXCLUDED.datetime,
    dynamic_content = EXCLUDED.dynamic_content,
    url = EXCLUDED.url,
    topic = EXCLUDED.topic,
    share_num = EXCLUDED.share_num,
    comment_num = EXCLUDED.comment_num,
    like_num = EXCLUDED.like_num,
    comment_id = EXCLUDED.comment_id,
    comment_type = EXCLUDED.comment_type,
    heat = EXCLUDED.heat;
"""

# --- videos_table ---
create_videos_table_sql = """CREATE TABLE IF NOT EXISTS videos_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    name text,
    bvid varchar(50) UNIQUE, -- Bilibili Video ID
    timestamp bigint,
    datetime timestamp,
    video_name text,
    description text,
    cover text,
    play_num bigint,
    comment_num bigint,
    like_num bigint,
    coin bigint,
    favorite_num bigint,
    share_num bigint,
    danmuku_num bigint,
    aid varchar(50),
    length varchar(20), -- e.g., "HH:MM:SS"
    honor_short text,
    honor_count int,
    honor text,
    video_ai_conclusion text,
    heat float
);"""

insert_videos_table_sql = """INSERT INTO videos_table (
    uid, name, bvid, timestamp, datetime, video_name, description, cover, play_num, comment_num, like_num, coin, favorite_num, share_num, danmuku_num, aid, length, honor_short, honor_count, honor, video_ai_conclusion, heat
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
) ON CONFLICT (bvid) DO UPDATE SET
    name = EXCLUDED.name,
    timestamp = EXCLUDED.timestamp,
    datetime = EXCLUDED.datetime,
    video_name = EXCLUDED.video_name,
    description = EXCLUDED.description,
    cover = EXCLUDED.cover,
    play_num = EXCLUDED.play_num,
    comment_num = EXCLUDED.comment_num,
    like_num = EXCLUDED.like_num,
    coin = EXCLUDED.coin,
    favorite_num = EXCLUDED.favorite_num,
    share_num = EXCLUDED.share_num,
    danmuku_num = EXCLUDED.danmuku_num,
    aid = EXCLUDED.aid,
    length = EXCLUDED.length,
    honor_short = EXCLUDED.honor_short,
    honor_count = EXCLUDED.honor_count,
    honor = EXCLUDED.honor,
    video_ai_conclusion = EXCLUDED.video_ai_conclusion,
    heat = EXCLUDED.heat;
"""

# --- video_comment_table (Dynamic by up_uid) ---
def get_video_comment_table_name(up_uid: str) -> str:
    return f"video_comment_{up_uid}"

def create_video_comment_table_sql(up_uid: str) -> str:
    table_name = get_video_comment_table_name(up_uid)
    return f"""CREATE TABLE IF NOT EXISTS {table_name} (
        id serial4 PRIMARY KEY,
        up_uid varchar(50),
        up_name text,
        oid varchar(50),
        bvid varchar(50) NOT NULL,
        from_video_title text,
        rpid varchar(100) UNIQUE, -- Assuming rpid is unique within this specific table
        mid varchar(50),
        uname varchar(100),
        face text,
        timestamp bigint,
        datetime timestamp,
        like_num bigint,
        comment text,
        rcount int,
        parent_rpid text,
        is_sub_comment bool,
        heat bigint,
        sentiment float
    );"""

def create_video_comment_indexes_sql(up_uid: str) -> str:
    """Create performance indexes for video comment table (optimized for both top comments and top users)"""
    table_name = get_video_comment_table_name(up_uid)
    return f"""
        CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime ON {table_name} (datetime);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_like_num ON {table_name} (like_num DESC);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_heat ON {table_name} (heat DESC);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime_heat ON {table_name} (datetime, heat DESC);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_bvid ON {table_name} (bvid);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_mid_datetime ON {table_name} (mid, datetime);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_uname_datetime ON {table_name} (uname, datetime);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_rcount ON {table_name} (rcount DESC);
    """

def insert_video_comment_table_sql(up_uid: str) -> str:
    table_name = get_video_comment_table_name(up_uid)
    return f"""INSERT INTO {table_name} (
        up_uid, up_name, oid, bvid, from_video_title, rpid, mid, uname, face, timestamp, datetime, like_num, comment, rcount, parent_rpid, is_sub_comment, heat, sentiment
    ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
    ) ON CONFLICT (rpid) DO UPDATE SET
        up_name = EXCLUDED.up_name,
        timestamp = EXCLUDED.timestamp,
        datetime = EXCLUDED.datetime,
        like_num = EXCLUDED.like_num,
        comment = EXCLUDED.comment,
        rcount = EXCLUDED.rcount,
        is_sub_comment = EXCLUDED.is_sub_comment,
        heat = EXCLUDED.heat,
        sentiment = EXCLUDED.sentiment;
    """

def insert_or_update_video_comment_sentiment_sql(up_uid: str) -> str:
    table_name = get_video_comment_table_name(up_uid)
    return f"""INSERT INTO {table_name} (
        up_uid, oid, rpid, mid, uname, face, timestamp, datetime, like_num, comment, rcount, parent_rpid, is_sub_comment, heat, sentiment
    ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
    ) ON CONFLICT (rpid) DO UPDATE SET
        sentiment = EXCLUDED.sentiment;"""

# --- dynamics_comment_table (Dynamic by up_uid) ---
def get_dynamics_comment_table_name(up_uid: str) -> str:
    """根据 up_uid 生成 dynamics_comment 表名"""
    return f"dynamics_comment_{up_uid}"

def create_dynamics_comment_table_sql(up_uid: str) -> str:
    """根据 up_uid 生成创建 dynamics_comment 表的 SQL 语句"""
    table_name = get_dynamics_comment_table_name(up_uid)
    return f"""CREATE TABLE IF NOT EXISTS {table_name} (
        id serial4 PRIMARY KEY,
        up_uid varchar(50),
        up_name text,
        oid varchar(50),
        rpid varchar(100) UNIQUE, -- Assuming rpid is unique within this specific table
        from_dynamic text,
        mid varchar(50),
        uname varchar(100),
        face text,
        timestamp bigint,
        datetime timestamp,
        like_num bigint,
        comment text,
        rcount int,
        parent_rpid text,
        is_sub_comment bool,
        heat bigint,
        sentiment float
    );"""

def create_dynamics_comment_indexes_sql(up_uid: str) -> str:
    """Create performance indexes for dynamics comment table (optimized for both top comments and top users)"""
    table_name = get_dynamics_comment_table_name(up_uid)
    return f"""
        CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime ON {table_name} (datetime);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_like_num ON {table_name} (like_num DESC);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_heat ON {table_name} (heat DESC);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime_heat ON {table_name} (datetime, heat DESC);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_oid ON {table_name} (oid);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_mid_datetime ON {table_name} (mid, datetime);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_uname_datetime ON {table_name} (uname, datetime);
        CREATE INDEX IF NOT EXISTS idx_{table_name}_rcount ON {table_name} (rcount DESC);
    """

def insert_dynamics_comment_table_sql(up_uid: str) -> str:
    """根据 up_uid 生成插入 dynamics_comment 表的 SQL 语句"""
    table_name = get_dynamics_comment_table_name(up_uid)
    return f"""INSERT INTO {table_name} (
        up_uid, up_name, oid, rpid, from_dynamic, mid, uname, face, timestamp, datetime, like_num, comment, rcount, parent_rpid, is_sub_comment, heat, sentiment
    ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
    ) ON CONFLICT (rpid) DO NOTHING;""" # Avoid duplicate comments based on rpid

def insert_or_update_dynamics_comment_sentiment_sql(up_uid: str) -> str:
    """根据 up_uid 生成插入或更新 dynamics_comment 表 sentiment 字段的 SQL 语句"""
    table_name = get_dynamics_comment_table_name(up_uid)
    return f"""INSERT INTO {table_name} (
        up_uid, oid, rpid, mid, uname, face, timestamp, datetime, like_num, comment, rcount, parent_rpid, is_sub_comment, heat, sentiment
    ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
    ) ON CONFLICT (rpid) DO UPDATE SET
        sentiment = EXCLUDED.sentiment;"""

# --- user_info_table ---
create_user_info_table_sql = """CREATE TABLE IF NOT EXISTS user_info_table (
    uid varchar(50) PRIMARY KEY, -- User ID as primary key
    name text,
    face text,
    sign text,
    birthday varchar(10), -- e.g., "MM-DD"
    top_photo text,
    room_id varchar(20),
    live_url text
);"""

insert_user_info_table_sql = """INSERT INTO user_info_table (
    uid, name, face, sign, birthday, top_photo, room_id, live_url
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
) ON CONFLICT (uid) DO UPDATE SET
    name = EXCLUDED.name,
    face = EXCLUDED.face,
    sign = EXCLUDED.sign,
    birthday = EXCLUDED.birthday,
    top_photo = EXCLUDED.top_photo,
    room_id = EXCLUDED.room_id,
    live_url = EXCLUDED.live_url;""" # Update if user info changes

# --- tieba_whole_table (Dynamic by up_uid) ---
def get_tieba_whole_table_name(up_uid: str) -> str:
    safe_up_uid = ''.join(c for c in str(up_uid) if c.isalnum())
    # TODO support v qianniao ...
    return f"tieba_whole_{safe_up_uid}"

def get_create_tieba_whole_table_sql(up_uid: str) -> str:
    table_name = get_tieba_whole_table_name(up_uid)
    return f"""CREATE TABLE IF NOT EXISTS {table_name} (
        id serial4 PRIMARY KEY,
        up_uid varchar(50),
        fid varchar(50), -- Forum ID
        fname varchar(100), -- Forum Name
        tid varchar(50), -- Thread ID
        pid varchar(50) UNIQUE, -- Assuming pid is unique within this specific table
        user_name varchar(100),
        create_time timestamp,
        last_time timestamp,
        title text,
        text text, -- Post content
        img text,
        view_num bigint,
        reply_num bigint,
        agree bigint,
        disagree bigint,
        level_num int, -- User level
        floor int,
        sentiment float
    );"""

def get_insert_tieba_whole_table_sql(up_uid: str) -> str:
    table_name = get_tieba_whole_table_name(up_uid)
    return f"""INSERT INTO {table_name} (
        up_uid, fid, fname, tid, pid, user_name, create_time, last_time, title, text, img, view_num, reply_num, agree, disagree, level_num, floor
    ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
    ) ON CONFLICT (pid) DO UPDATE SET
        up_uid = EXCLUDED.up_uid,
        fid = EXCLUDED.fid,
        fname = EXCLUDED.fname,
        tid = EXCLUDED.tid,
        user_name = EXCLUDED.user_name,
        create_time = EXCLUDED.create_time,
        last_time = EXCLUDED.last_time,
        title = EXCLUDED.title,
        text = EXCLUDED.text,
        img = EXCLUDED.img,
        view_num = EXCLUDED.view_num,
        reply_num = EXCLUDED.reply_num,
        agree = EXCLUDED.agree,
        disagree = EXCLUDED.disagree,
        level_num = EXCLUDED.level_num,
        floor = EXCLUDED.floor;""" # Update all fields except sentiment on conflict

def get_insert_or_update_tieba_whole_table_sentiment_sql(up_uid: str) -> str:
    """根据 up_uid 生成插入或更新 tieba_whole 表 sentiment 字段的 SQL 语句"""
    table_name = get_tieba_whole_table_name(up_uid)
    # Include all columns in INSERT for robustness, especially if the row might not exist yet.
    # The ON CONFLICT clause ensures only sentiment is updated if the row exists.
    return f"""INSERT INTO {table_name} (
        up_uid, fid, fname, tid, pid, user_name, create_time, last_time, title, text, img, view_num, reply_num, agree, disagree, level_num, floor, sentiment
    ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
    ) ON CONFLICT (pid) DO UPDATE SET
        sentiment = EXCLUDED.sentiment;"""

# --- tieba_thread_table  ---
create_tieba_threads_table_sql = """CREATE TABLE IF NOT EXISTS tieba_threads_table (
        id serial4 PRIMARY KEY,
        up_uid varchar(50),
        fid varchar(50),
        fname varchar(100), -- Forum Name
        tid varchar(50) UNIQUE, -- Thread ID, assuming unique within this specific table
        user_name varchar(100), -- Thread author
        create_time timestamp,
        last_time timestamp, -- Last reply time
        title text,
        text text,
        img text,
        view_num bigint,
        reply_num bigint,
        share_num bigint,
        agree bigint,
        disagree bigint
    );"""

insert_tieba_threads_table_sql = """INSERT INTO tieba_threads_table (
        up_uid, fid, fname, tid, user_name, create_time, last_time, title, text, img, view_num, reply_num, share_num, agree, disagree
    ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
    ) ON CONFLICT (tid) DO UPDATE SET
        last_time = EXCLUDED.last_time,
        view_num = EXCLUDED.view_num,
        reply_num = EXCLUDED.reply_num,
        share_num = EXCLUDED.share_num,
        agree = EXCLUDED.agree,
        disagree = EXCLUDED.disagree;""" # Update thread stats if they change

# --- video_day_data_table ---
create_video_day_data_table_sql = """CREATE TABLE IF NOT EXISTS video_day_data_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    name text,
    bvid varchar(50) NOT NULL,
    title text,
    create_time bigint,
    datetime timestamp, -- YYYY-MM-DD
    view_num bigint,
    view_rise_num bigint,
    UNIQUE (uid, bvid, datetime)
);"""

insert_video_day_data_table_sql = """INSERT INTO video_day_data_table (
    uid, name, bvid, title, create_time, datetime, view_num, view_rise_num
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
) ON CONFLICT (uid, bvid, datetime) DO UPDATE SET
    view_num = EXCLUDED.view_num,
    view_rise_num = EXCLUDED.view_rise_num
"""

# --- AI_GEN_TABLE ---
create_ai_gen_table_sql = """CREATE TABLE IF NOT EXISTS ai_gen_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    name varchar(100),
    cur_datetime timestamp,
    cur_timestamp bigint,
    recent int,
    relationships text,
    tieba_summaries text,
    tieba_summaries_total text,
    rise_reason text,
    UNIQUE (uid, cur_datetime, recent) -- Assuming uid and datetime combination should be unique
);"""

insert_ai_gen_table_sql = """INSERT INTO ai_gen_table (
    uid, name, cur_datetime, cur_timestamp, recent, relationships, tieba_summaries, tieba_summaries_total, rise_reason
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9
) ON CONFLICT (uid, cur_datetime, recent) DO UPDATE SET
    name = EXCLUDED.name,
    cur_timestamp = EXCLUDED.cur_timestamp,
    recent = EXCLUDED.recent,
    relationships = EXCLUDED.relationships,
    tieba_summaries = EXCLUDED.tieba_summaries,
    tieba_summaries_total = EXCLUDED.tieba_summaries_total,
    rise_reason = EXCLUDED.rise_reason;"""

insert_or_update_relationships_sql = """INSERT INTO ai_gen_table (
    uid, name, cur_datetime, cur_timestamp, recent, relationships, tieba_summaries, tieba_summaries_total, rise_reason
) VALUES (
    $1, $2, $3, $4, $5, $6, NULL, NULL, NULL
) ON CONFLICT (uid, cur_datetime, recent) DO UPDATE SET
    relationships = EXCLUDED.relationships;"""

insert_or_update_tieba_summaries_sql = """INSERT INTO ai_gen_table (
    uid, name, cur_datetime, cur_timestamp, recent, relationships, tieba_summaries, tieba_summaries_total, rise_reason
) VALUES (
    $1, $2, $3, $4, $5, NULL, $6, NULL, NULL
) ON CONFLICT (uid, cur_datetime, recent) DO UPDATE SET
    tieba_summaries = EXCLUDED.tieba_summaries;"""

insert_or_update_tieba_summaries_total_sql = """INSERT INTO ai_gen_table (
    uid, name, cur_datetime, cur_timestamp, recent, relationships, tieba_summaries, tieba_summaries_total, rise_reason
) VALUES (
    $1, $2, $3, $4, $5, NULL, NULL, $6, NULL
) ON CONFLICT (uid, cur_datetime, recent) DO UPDATE SET
    tieba_summaries_total = EXCLUDED.tieba_summaries_total;"""

insert_or_update_rise_reason_sql = """INSERT INTO ai_gen_table (
    uid, name, cur_datetime, cur_timestamp, recent, relationships, tieba_summaries, tieba_summaries_total, rise_reason
) VALUES (
    $1, $2, $3, $4, $5, NULL, NULL, NULL, $6
) ON CONFLICT (uid, cur_datetime, recent) DO UPDATE SET
    rise_reason = EXCLUDED.rise_reason;"""

# --- fans_medal_rank_table ---
create_fans_medal_rank_table_sql = """CREATE TABLE IF NOT EXISTS fans_medal_rank_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    name varchar(100),
    liveid varchar(50),
    datetime timestamp NOT NULL,
    rank_list jsonb,
    UNIQUE (uid, datetime)
);"""

insert_fans_medal_rank_table_sql = """INSERT INTO fans_medal_rank_table (
    uid, name, liveid, datetime, rank_list
) VALUES (
    $1, $2, $3, $4, $5
) ON CONFLICT (uid, datetime) DO UPDATE SET
    name = EXCLUDED.name,
    liveid = EXCLUDED.liveid,
    rank_list = EXCLUDED.rank_list;"""

# --- followers_list_table ---
create_followers_list_table_sql = """CREATE TABLE IF NOT EXISTS followers_list_table (
    id serial4 PRIMARY KEY,
    up_uid varchar(50) NOT NULL, -- The UID of the user being followed
    follower_uid varchar(50) NOT NULL, -- The UID of the follower
    follower_name text,
    face_url text, -- URL of the follower's avatar
    sign text, -- Follower's signature/bio
    follow_time bigint, -- Timestamp of when the follow action occurred, if available from API
    record_timestamp timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, -- When this record was fetched/inserted
    UNIQUE (up_uid, follower_uid)
);"""

insert_follower_sql = """INSERT INTO followers_list_table (
    up_uid, follower_uid, follower_name, face_url, sign, follow_time, record_timestamp
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) ON CONFLICT (up_uid, follower_uid) DO UPDATE SET
    follower_name = EXCLUDED.follower_name,
    face_url = EXCLUDED.face_url,
    sign = EXCLUDED.sign,
    follow_time = EXCLUDED.follow_time,
    record_timestamp = EXCLUDED.record_timestamp;"""

# SQL to delete all followers for a specific up_uid. This can be used before inserting a fresh list.
delete_followers_by_up_uid_sql = """DELETE FROM followers_list_table WHERE up_uid = $1;""" # Changed %s to $1

# --- follower_review_table ---
create_follower_review_table_sql = """CREATE TABLE IF NOT EXISTS follower_review_table (
    id serial4 PRIMARY KEY,
    up_uid varchar(50) NOT NULL,
    up_name text,
    query_date date NOT NULL,
    recent_days int,
    follower_uid varchar(50) NOT NULL,
    follower_name text,
    if_review boolean DEFAULT FALSE,
    UNIQUE (up_uid, follower_uid, query_date)
);"""

insert_or_update_follower_review_sql = """INSERT INTO follower_review_table (
    up_uid, up_name, query_date, recent_days, follower_uid, follower_name, if_review
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) ON CONFLICT (up_uid, follower_uid, query_date) DO UPDATE SET
    up_name = EXCLUDED.up_name,
    recent_days = EXCLUDED.recent_days,
    follower_name = EXCLUDED.follower_name,
    if_review = EXCLUDED.if_review;"""

# --- vup_recent_info_view --- # TODO
create_vup_recent_info_view_sql = """CREATE OR REPLACE VIEW vup_recent_info_view AS
SELECT
    uit.uid,
    uit.name AS user_name,
    uit.face AS user_face,
    uit.sign AS user_sign,
    uit.room_id,
    uit.live_url,
    cst.follower_num,
    cst.dahanghai_num,
    cst.video_total_num,
    cst.article_total_num,
    cst.likes_total_num,
    dt.timestamp AS dynamic_timestamp,
    dt.dynamic_content,
    dt.url AS dynamic_url,
    dt.topic AS dynamic_topic,
    dt.dynamic_id,
    dt.share_num AS dynamic_share_num,
    dt.comment_num AS dynamic_comment_num,
    dt.like_num AS dynamic_like_num,
    vt.bvid,
    vt.timestamp AS video_timestamp,
    vt.video_name,
    vt.play_num AS video_play_num,
    vt.comment_num AS video_comment_num,
    vt.like_num AS video_like_num,
    vt.coin AS video_coin,
    vt.favorite_num AS video_favorite_num,
    vt.share_num AS video_share_num,
    vt.danmuku_num AS video_danmuku_num,
    ait.recent AS ai_recent_summary,
    ait.relationships AS ai_relationships_summary,
    ait.tieba_summaries AS ai_tieba_summary,
    ait.tieba_summaries_total AS ai_tieba_total_summary,
    ait.rise_reason AS ai_rise_reason
FROM
    user_info_table uit
LEFT JOIN
    current_stat_table cst ON uit.uid = cst.uid
LEFT JOIN
    (SELECT DISTINCT ON (uid) * FROM dynamics_table ORDER BY uid, timestamp DESC) AS dt ON uit.uid = dt.uid
LEFT JOIN
    (SELECT DISTINCT ON (uid) * FROM videos_table ORDER BY uid, timestamp DESC) AS vt ON uit.uid = vt.uid
LEFT JOIN
    (SELECT DISTINCT ON (uid) * FROM ai_gen_table ORDER BY uid, cur_timestamp DESC) AS ait ON uit.uid = ait.uid;
"""

# --- dahanghai_list_table ---
create_dahanghai_list_table_sql = """CREATE TABLE IF NOT EXISTS dahanghai_list_table (
    id serial4 PRIMARY KEY,
    up_uid VARCHAR(50) NOT NULL,
    up_name TEXT,
    time DATE,
    datetime TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    num BIGINT,
    page INT,
    uid BIGINT NOT NULL,
    ruid BIGINT,
    rank INT,
    username TEXT,
    face TEXT,
    guard_level INT,
    guard_sub_level INT,
    if_top3 BOOLEAN,
    UNIQUE (up_uid, datetime, uid)
);

CREATE INDEX IF NOT EXISTS idx_dahanghai_list_up_uid_datetime ON dahanghai_list_table (up_uid, datetime DESC);
CREATE INDEX IF NOT EXISTS idx_dahanghai_list_up_uid_time ON dahanghai_list_table (up_uid, time);
"""

insert_dahanghai_list_table_sql = """INSERT INTO dahanghai_list_table (
    up_uid, up_name, time, datetime, num, page, uid, ruid, rank, username, face, guard_level, guard_sub_level, if_top3
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
) ON CONFLICT (up_uid, datetime, uid) DO UPDATE SET
    up_name = EXCLUDED.up_name,
    time = EXCLUDED.time,
    num = EXCLUDED.num,
    page = EXCLUDED.page,
    ruid = EXCLUDED.ruid,
    rank = EXCLUDED.rank,
    username = EXCLUDED.username,
    face = EXCLUDED.face,
    guard_level = EXCLUDED.guard_level,
    guard_sub_level = EXCLUDED.guard_sub_level,
    if_top3 = EXCLUDED.if_top3;
"""
