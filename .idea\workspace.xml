<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="f174b455-4493-4b1a-bb07-97b718d3ecab" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/vups/algos/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/vups/algos/actions/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pyproject.toml" beforeDir="false" afterPath="$PROJECT_DIR$/pyproject.toml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruff.toml" beforeDir="false" afterPath="$PROJECT_DIR$/ruff.toml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/vups/base/action/test_relation_sum.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/vups/base/action/test_relation_sum.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/uv.lock" beforeDir="false" afterPath="$PROJECT_DIR$/uv.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/vups/base/action/reasoning_of_fans.py" beforeDir="false" afterPath="$PROJECT_DIR$/vups/algos/actions/reasoning_of_fans.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/vups/base/action/relation_sum.py" beforeDir="false" afterPath="$PROJECT_DIR$/vups/algos/actions/relation_sum.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/vups/utils/bi_utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/vups/utils/bi_utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/vups_mcp/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/vups_server/config/const.py" beforeDir="false" afterPath="$PROJECT_DIR$/vups_server/config/const.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 3
}]]></component>
  <component name="ProjectId" id="31r5TatGbQqdOUci0UIrVa6c7hW" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "G:/Code/vups",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="G:\Code\vups\vups\algos\actions" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.17890.14" />
        <option value="bundled-python-sdk-5b207ade9991-7e9c3bbb6e34-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.17890.14" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f174b455-4493-4b1a-bb07-97b718d3ecab" name="更改" comment="" />
      <created>1756269359385</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756269359385</updated>
      <workItem from="1756269360502" duration="73000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>