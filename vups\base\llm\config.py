import os
from dataclasses import dataclass

from pathlib import Path
from dotenv import load_dotenv

@dataclass
class LLMConfig:
    # Initialize all fields as empty strings
    hunyuan_api_base: str = ""
    hunyuan_api_key: str = ""
    anthropic_api_base: str = ""
    anthropic_api_key: str = ""
    baichuan_api_base: str = ""
    baichuan_api_key: str = ""
    openrouter_api_base: str = ""
    openrouter_api_key: str = ""
    ollama_api_base: str = ""
    ollama_api_key: str = ""
    llama_api_base: str = ""
    llama_api_key: str = ""

    def __post_init__(self):
        self._try_load_env()
        self._load_config()

    @staticmethod
    def _try_load_env():
        possible_env_paths = [
            Path.cwd() / ".env",
            Path.home() / ".vups" / ".env",
        ]

        for env_path in possible_env_paths:
            if env_path.is_file():
                load_dotenv(dotenv_path=env_path)
                break

    def _load_config(self):
        """Load all configurations from environment variables"""
        # Hunyuan
        self.hunyuan_api_base = os.getenv("HUNYUAN_OPENAPI_URL", "")
        self.hunyuan_api_key = os.getenv("HUNYUAN_OPENAPI_KEY", "")

        # Anthropic
        self.anthropic_api_base = os.getenv("ANTHROPIC_API_URL", "")
        self.anthropic_api_key = os.getenv("ANTHROPIC_API_KEY", "")

        # Baichuan
        self.baichuan_api_base = os.getenv("BAICHUAN_API_BASE", "")
        self.baichuan_api_key = os.getenv("BAICHUAN_API_KEY", "")

        # OpenRouter
        self.openrouter_api_base = os.getenv("OPENROUTER_API_BASE", "")
        self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY", "")

        # Ollama
        self.ollama_api_base = os.getenv("OLLAMA_API_BASE", "")
        self.ollama_api_key = os.getenv("OLLAMA_API_KEY", "")

        # Llama
        self.llama_api_base = os.getenv("LLAMA_API_BASE", "")
        self.llama_api_key = os.getenv("LLAMA_API_KEY", "")

    @classmethod
    def from_env(cls):
        """Create config from environment variables"""
        return cls()

    @classmethod
    def from_dict(cls, config_dict: dict):
        """Create config from dictionary"""
        return cls(**{k: v for k, v in config_dict.items() if hasattr(cls, k)})

# Global default config
default_config = LLMConfig.from_env()
