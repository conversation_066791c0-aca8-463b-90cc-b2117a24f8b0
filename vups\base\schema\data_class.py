from dataclasses import dataclass, field


@dataclass
class VupRencentInfo:
    time: str = ""
    name: str = ""
    follower_change: int = 0
    dahanghai_change: int = 0
    video_content: list = field(default_factory=list)
    dynamic_content: list = field(default_factory=list)
    live_content: str = ""
    relations: list = field(default_factory=list)
    rise_videos: list = field(default_factory=list)
    tieba_topic: list = field(default_factory=list)
    # sensiment
