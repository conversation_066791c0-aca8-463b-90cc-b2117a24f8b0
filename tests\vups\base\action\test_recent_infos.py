from vups.algos.actions.reasoning_of_fans import ReasoningOfFans
from vups_server.query.query_vup_user_data import query_recent_info


async def model_test():
    char_zh = "星瞳"
    mid = "401315430"
    recent_info = await query_recent_info(mid, "22886883")

    rs = ReasoningOfFans(char_zh, "hunyuan-standard-256K")
    res = await rs.run(recent_info)

if __name__ == '__main__':
    # unittest.main()
    import asyncio
    asyncio.run(model_test())
