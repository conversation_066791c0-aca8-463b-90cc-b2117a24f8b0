"""
Cookie Manager Module

Manages multiple cookie sets for different data collection tasks
"""

import asyncio
import json
import os
import time
import threading
from typing import Dict, Optional, List
from datetime import datetime, timedelta
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from vups.logger import logger

from .cookie_refresh import BilibiliCookieRefresher


class CookieManager:
    """
    Cookie Manager for multiple data collection tasks
    """
    
    def __init__(self, config_path: Optional[str] = None):
        # Use server package config if no path provided
        if config_path is None:
            from vups_server.config import get_config
            server_config = get_config()
            config_path = server_config.cookie_config_path

        self.config_path = config_path
        self.config = {}
        self.scheduler = None
        self.lock = threading.Lock()
        self.refresher = None

        # Load configuration
        self.load_config()
        
    def load_config(self):
        """Load cookie configuration from file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                logger.info(f"Loaded cookie configuration from {self.config_path}")
            else:
                logger.warning(f"Cookie config file not found: {self.config_path}")
                self.config = {"cookies": {}, "refresh_settings": {}}
        except Exception as e:
            logger.error(f"Error loading cookie config: {e}")
            self.config = {"cookies": {}, "refresh_settings": {}}
    
    def save_config(self):
        """Save cookie configuration to file"""
        try:
            with self.lock:
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                logger.info(f"Saved cookie configuration to {self.config_path}")
        except Exception as e:
            logger.error(f"Error saving cookie config: {e}")
    
    def get_cookie_for_task(self, task_type: str) -> Optional[str]:
        """
        Get cookie string for specific task type
        
        Args:
            task_type: Task type ('user', 'creator', 'live')
            
        Returns:
            Cookie string or None if not available
        """
        try:
            cookie_data = self.config.get("cookies", {}).get(task_type)
            if not cookie_data or not cookie_data.get("enabled", False):
                logger.warning(f"Cookie for task '{task_type}' not available or disabled")
                return None
                
            # Build cookie string
            cookie_parts = []
            for key in ["SESSDATA", "bili_jct", "buvid3", "buvid4", "DedeUserID", "b_nut", "sid"]:
                value = cookie_data.get(key)
                if value:
                    cookie_parts.append(f"{key}={value}")
            
            if not cookie_parts:
                logger.warning(f"No valid cookie data for task '{task_type}'")
                return None
                
            cookie_str = "; ".join(cookie_parts)
            logger.debug(f"Retrieved cookie for task '{task_type}'")
            return cookie_str
            
        except Exception as e:
            logger.error(f"Error getting cookie for task '{task_type}': {e}")
            return None
    
    def get_cookie_data(self, task_type: str) -> Optional[Dict]:
        """
        Get complete cookie data for specific task type
        
        Args:
            task_type: Task type ('user', 'creator', 'live')
            
        Returns:
            Cookie data dict or None if not available
        """
        try:
            cookie_data = self.config.get("cookies", {}).get(task_type)
            if not cookie_data or not cookie_data.get("enabled", False):
                return None
            return cookie_data.copy()
        except Exception as e:
            logger.error(f"Error getting cookie data for task '{task_type}': {e}")
            return None
    
    def update_cookie_data(self, task_type: str, cookie_data: Dict):
        """
        Update cookie data for specific task type
        
        Args:
            task_type: Task type ('user', 'creator', 'live')
            cookie_data: Updated cookie data
        """
        try:
            with self.lock:
                if "cookies" not in self.config:
                    self.config["cookies"] = {}
                    
                self.config["cookies"][task_type] = cookie_data
                self.save_config()
                logger.info(f"Updated cookie data for task '{task_type}'")
        except Exception as e:
            logger.error(f"Error updating cookie data for task '{task_type}': {e}")
    
    def get_enabled_tasks(self) -> List[str]:
        """Get list of enabled task types"""
        enabled_tasks = []
        for task_type, cookie_data in self.config.get("cookies", {}).items():
            if cookie_data.get("enabled", False):
                enabled_tasks.append(task_type)
        return enabled_tasks
    
    def is_refresh_needed(self, task_type: str) -> bool:
        """
        Check if cookie refresh is needed for specific task
        
        Args:
            task_type: Task type to check
            
        Returns:
            True if refresh is needed
        """
        try:
            cookie_data = self.get_cookie_data(task_type)
            if not cookie_data:
                return False
                
            last_refresh = cookie_data.get("last_refresh")
            if not last_refresh:
                return True  # Never refreshed
                
            refresh_settings = self.config.get("refresh_settings", {})
            check_interval_hours = refresh_settings.get("check_interval_hours", 24)
            
            # Check if enough time has passed since last refresh
            time_since_refresh = time.time() - last_refresh
            return time_since_refresh >= (check_interval_hours * 3600)
            
        except Exception as e:
            logger.error(f"Error checking refresh need for task '{task_type}': {e}")
            return False
    
    async def refresh_cookie_for_task(self, task_type: str) -> bool:
        """
        Refresh cookie for specific task type

        Args:
            task_type: Task type to refresh
            
        Returns:
            True if successful
        """
        try:
            cookie_data = self.get_cookie_data(task_type)
            if not cookie_data:
                logger.warning(f"No cookie data available for task '{task_type}'")
                return False
                
            if not cookie_data.get("refresh_token"):
                logger.warning(f"No refresh_token available for task '{task_type}', skipping refresh")
                return False
                
            logger.info(f"Starting cookie refresh for task '{task_type}'")
            
            if not self.refresher:
                if BilibiliCookieRefresher is None:
                    logger.warning("Cookie refresher not available in standalone mode")
                    return False
                self.refresher = BilibiliCookieRefresher()

            async with self.refresher as refresher:
                updated_data = await refresher.full_refresh_process(cookie_data)
                
            if updated_data:
                self.update_cookie_data(task_type, updated_data)
                logger.info(f"Cookie refresh successful for task '{task_type}'")
                return True
            else:
                logger.error(f"Cookie refresh failed for task '{task_type}'")
                return False
                
        except Exception as e:
            logger.error(f"Error refreshing cookie for task '{task_type}': {e}")
            return False
    
    async def refresh_all_cookies(self):
        """
        Refresh all enabled cookies that need refreshing
        """
        logger.info("Starting batch cookie refresh check")
        
        enabled_tasks = self.get_enabled_tasks()
        refresh_tasks = []
        
        for task_type in enabled_tasks:
            if self.is_refresh_needed(task_type):
                refresh_tasks.append(task_type)
        
        if not refresh_tasks:
            logger.info("No cookies need refreshing")
            return
            
        logger.info(f"Refreshing cookies for tasks: {refresh_tasks}")
        
        # Refresh cookies sequentially to avoid rate limiting
        for task_type in refresh_tasks:
            try:
                await self.refresh_cookie_for_task(task_type)
                # Add delay between refreshes
                await asyncio.sleep(5)
            except Exception as e:
                logger.error(f"Error refreshing cookie for task '{task_type}': {e}")
    
    def start_auto_refresh(self):
        """Start automatic cookie refresh scheduler"""
        if self.scheduler and self.scheduler.running:
            logger.warning("Cookie refresh scheduler is already running")
            return
            
        try:
            refresh_settings = self.config.get("refresh_settings", {})
            check_interval_hours = refresh_settings.get("check_interval_hours", 24)
            
            self.scheduler = AsyncIOScheduler()
            self.scheduler.add_job(
                self.refresh_all_cookies,
                IntervalTrigger(hours=check_interval_hours),
                id='cookie_auto_refresh',
                name='Cookie Auto Refresh',
                next_run_time=datetime.now() + timedelta(minutes=1)  # Start after 1 minute
            )
            
            self.scheduler.start()
            logger.info(f"Started cookie auto-refresh scheduler (interval: {check_interval_hours} hours)")
            
        except Exception as e:
            logger.error(f"Error starting cookie refresh scheduler: {e}")
    
    def stop_auto_refresh(self):
        """Stop automatic cookie refresh scheduler"""
        if self.scheduler and self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("Stopped cookie auto-refresh scheduler")
    
    def get_status(self) -> Dict:
        """Get status of all cookies"""
        status = {
            "enabled_tasks": self.get_enabled_tasks(),
            "cookies": {}
        }
        
        for task_type in self.config.get("cookies", {}):
            cookie_data = self.config["cookies"][task_type]
            status["cookies"][task_type] = {
                "enabled": cookie_data.get("enabled", False),
                "has_refresh_token": bool(cookie_data.get("refresh_token")),
                "last_refresh": cookie_data.get("last_refresh"),
                "needs_refresh": self.is_refresh_needed(task_type) if cookie_data.get("enabled") else False
            }
        
        return status


# Global cookie manager instance
_cookie_manager = None

def get_cookie_manager() -> CookieManager:
    """Get global cookie manager instance"""
    global _cookie_manager
    if _cookie_manager is None:
        _cookie_manager = CookieManager()
    return _cookie_manager

def get_task_cookie(task_type: str) -> Optional[str]:
    """
    Convenience function to get cookie for task type

    Args:
        task_type: Task type ('user', 'creator', 'live')

    Returns:
        Cookie string or None
    """
    return get_cookie_manager().get_cookie_for_task(task_type)

def get_cookie_field(task_type: str, field_name: str, default: str = "") -> str:
    """
    Get specific cookie field for task type

    Args:
        task_type: Task type ('user', 'creator', 'live')
        field_name: Cookie field name (e.g., 'SESSDATA', 'bili_jct', etc.)
        default: Default value if field not found

    Returns:
        Cookie field value or default
    """
    try:
        cookie_data = get_cookie_manager().get_cookie_data(task_type)
        if cookie_data:
            return cookie_data.get(field_name, default)
        return default
    except Exception as e:
        logger.error(f"Error getting cookie field '{field_name}' for task '{task_type}': {e}")
        return default

# Convenience functions for common cookie fields
def get_sessdata(task_type: str = "user") -> str:
    """Get SESSDATA for task type"""
    return get_cookie_field(task_type, "SESSDATA")

def get_bili_jct(task_type: str = "user") -> str:
    """Get bili_jct for task type"""
    return get_cookie_field(task_type, "bili_jct")

def get_buvid3(task_type: str = "user") -> str:
    """Get buvid3 for task type"""
    return get_cookie_field(task_type, "buvid3")

def get_buvid4(task_type: str = "user") -> str:
    """Get buvid4 for task type"""
    return get_cookie_field(task_type, "buvid4")

def get_dedeuserid(task_type: str = "user") -> str:
    """Get DedeUserID for task type"""
    return get_cookie_field(task_type, "DedeUserID")

def get_b_nut(task_type: str = "user") -> str:
    """Get b_nut for task type"""
    return get_cookie_field(task_type, "b_nut")

def get_sid(task_type: str = "user") -> str:
    """Get sid for task type"""
    return get_cookie_field(task_type, "sid")
