import asyncio
import os
import csv
import math
import datetime
from bilibili_api import Credential, comment, sync, video
import time

from vups_server.base.cookie_manager import get_sessdata, get_bili_jct, get_buvid3, get_dedeuserid
import vups.utils as U
from vups.logger import logger


async def fetch_single_video_comments(
    mid: str, oid: str, video_title: str, char_zh: str, credential: Credential
):
    """
    获取指定视频 (oid/bvid) 的评论，存入数据库.
    Fetches comments for a specific video (aid/bvid) and stores them in the database.

    :param oid: 视频 ID (aid 或 bvid)
    :return: None
    """
    logger.info(f"开始为视频 {oid} 获取评论并存入数据库")

    # 1. 处理 BVID
    if oid.startswith("BV"):
        target_bv = oid
        aid = U.bv2av(oid)
        target_oid_for_api = str(aid)
        target_oid_for_db = str(aid)
    else:
        target_bv = U.av2bv(oid)
        target_oid_for_api = str(oid)
        target_oid_for_db = str(oid)

    # 3. 获取评论
    comments_to_insert = []
    page = 1
    api_comment_count = 0
    total_api_comments = 0
    processed_rpids = (
        set()
    )  # Track rpids processed in this run to avoid duplicates within the run

    api_resource_type = comment.CommentResourceType.VIDEO

    while True:
        try:
            logger.info(f"获取视频评论: 对象 {target_oid_for_api}, page: {page}")
            c = await comment.get_comments(
                target_oid_for_api,
                api_resource_type,
                page,
                comment.OrderType.LIKE,
                credential=credential,
            )
            await asyncio.sleep(3)  # API rate limit

            if page == 1:
                total_api_comments = c.get("page", {}).get("count", 0)
                logger.info(
                    f"视频 {target_oid_for_api} 共有 {total_api_comments} 条根评论"
                )

            replies = c.get("replies")
            if not replies:
                logger.info(
                    f"视频 {target_oid_for_api} 第 {page} 页无评论，或已获取完毕"
                )
                break

            api_comment_count += len(replies)
            logger.info(
                f"API page {page}: 获取到 {len(replies)} 条根评论 (累计 {api_comment_count} / 总计 {total_api_comments})"
            )

            for cmt in replies:
                rpid_str = str(cmt.get("rpid"))
                if rpid_str and rpid_str not in processed_rpids:  # Corrected logic
                    pub_timestamp = cmt.get("ctime")
                    pub_datetime = (
                        datetime.datetime.fromtimestamp(pub_timestamp)
                        if pub_timestamp
                        else None
                    )

                    comment_data = {
                        "up_uid": mid,
                        "up_name": char_zh,
                        "oid": target_oid_for_db,
                        "bvid": target_bv,
                        "rpid": rpid_str,
                        "from_video_title": video_title,
                        "mid": str(cmt.get("member", {}).get("mid")),
                        "uname": cmt.get("member", {}).get("uname"),
                        "face": cmt.get("member", {}).get("avatar"),
                        "timestamp": pub_timestamp,
                        "datetime": pub_datetime,
                        "like_num": cmt.get("like", 0),
                        "comment": cmt.get("content", {})
                        .get("message", "")
                        .replace("\n", " "),
                        "rcount": cmt.get("rcount", 0),
                        "parent_rpid": "0",
                        "is_sub_comment": False,
                        "heat": U.safe_int(cmt.get("like", 0))
                        + U.safe_int(cmt.get("rcount", 0)),
                        "sentiment": None,
                    }
                    comments_to_insert.append(comment_data)
                    processed_rpids.add(rpid_str)

                    # 获取子评论 (楼中楼)
                    if cmt.get("rcount", 0) > 0:
                        sub_cmt_api = comment.Comment(
                            oid=target_oid_for_api,
                            type_=api_resource_type,
                            rpid=cmt["rpid"],
                            credential=credential,
                        )
                        sub_page = 1
                        sub_comment_count = 0
                        total_sub_comments = cmt["rcount"]

                        while True:
                            try:
                                logger.debug(
                                    f"获取子评论: 根 rpid {rpid_str}, sub_page: {sub_page}"
                                )
                                rcoms = await sub_cmt_api.get_sub_comments(sub_page)
                                await asyncio.sleep(2)  # API rate limit

                                sub_replies = rcoms.get("replies")
                                if not sub_replies:
                                    logger.debug(
                                        f"根 rpid {rpid_str} 第 {sub_page} 页无子评论"
                                    )
                                    break

                                sub_comment_count += len(sub_replies)

                                for rcom in sub_replies:
                                    sub_rpid_str = str(rcom.get("rpid"))
                                    if (
                                        sub_rpid_str
                                        and sub_rpid_str not in processed_rpids
                                    ):  # Corrected logic
                                        sub_pub_timestamp = rcom.get("ctime")
                                        sub_pub_datetime = (
                                            datetime.datetime.fromtimestamp(
                                                sub_pub_timestamp
                                            )
                                            if sub_pub_timestamp
                                            else None
                                        )

                                        sub_comment_data = {
                                            "up_uid": mid,
                                            "up_name": char_zh,
                                            "oid": target_oid_for_db,
                                            "bvid": target_bv,
                                            "rpid": sub_rpid_str,
                                            "from_video_title": video_title,
                                            "mid": str(
                                                rcom.get("member", {}).get("mid")
                                            ),
                                            "uname": rcom.get("member", {}).get(
                                                "uname"
                                            ),
                                            "face": rcom.get("member", {}).get(
                                                "avatar"
                                            ),
                                            "timestamp": sub_pub_timestamp,
                                            "datetime": sub_pub_datetime,
                                            "like_num": rcom.get("like", 0),
                                            "comment": rcom.get("content", {})
                                            .get("message", "")
                                            .replace("\n", " "),
                                            "rcount": 0,  # Sub-comments don't have rcount in the same way
                                            "parent_rpid": rpid_str,  # Link to parent comment
                                            "is_sub_comment": True,
                                            "heat": U.safe_int(rcom.get("like", 0))
                                            + U.safe_int(rcom.get("rcount", 0)),
                                            "sentiment": None,
                                        }
                                        comments_to_insert.append(sub_comment_data)
                                        processed_rpids.add(sub_rpid_str)

                                if sub_comment_count >= total_sub_comments:
                                    break  # Got all expected sub-comments

                                sub_page += 1
                                if sub_page > 50:  # Limit sub-pages
                                    logger.warning(
                                        f"子评论获取超过 50 页，停止获取 rpid {rpid_str} 的子评论"
                                    )
                                    break

                            except Exception as sub_ex:
                                logger.error(
                                    f"获取 rpid {rpid_str} 的子评论 page {sub_page} 时出错: {sub_ex}"
                                )
                                break

            # 检查是否需要获取下一页根评论
            if api_comment_count >= total_api_comments and total_api_comments > 0:
                logger.info(f"已获取所有 {total_api_comments} 条根评论")
                break

            page += 1
            if page > 250:  # Limit pages
                logger.warning("视频评论获取超过 250 页，停止获取")
                break

        except Exception as ex:
            logger.error(
                f"获取视频 {target_oid_for_api} 的评论 page {page} 时出错: {ex}"
            )
            break

    logger.info(f"视频 {oid} 的评论获取完成")
    return comments_to_insert


async def fetch_single_dynamic_comments(
    mid: str,
    oid: str,
    dynamic_content: str,
    comment_id: str,
    comment_type: int,
    char_zh: str,
    credential: Credential,
):
    """
    获取指定动态 (Opus ID) 的评论，存入数据库.
    Fetches comments for a specific dynamic (Opus ID) and stores them in the database.

    :param oid: 动态 ID (Opus ID)
    :return: None
    """
    logger.info(f"开始为动态 {oid} 获取评论并存入数据库")

    target_oid_for_api = oid
    target_oid_for_db = oid
    comment_id = U.safe_int(comment_id)
    comment_type = comment.CommentResourceType(comment_type)
    if comment_id == 0:
        logger.warning(f"动态 {oid} 的 comment_id 为 0，无法获取评论")
        return

    # 2. 获取评论
    comments_to_insert = []
    page = 1
    api_comment_count = 0
    total_api_comments = 0
    processed_rpids = set()  # Track rpids processed in this run

    while True:
        try:
            logger.info(f"获取动态评论: 对象 {target_oid_for_api}, page: {page}")
            c = await comment.get_comments(
                comment_id,
                comment_type,
                page,
                comment.OrderType.LIKE,
                credential=credential,
            )
            await asyncio.sleep(3)  # API rate limit

            if page == 1:
                total_api_comments = c.get("page", {}).get("count", 0)
                logger.info(
                    f"动态 {target_oid_for_api} 共有 {total_api_comments} 条根评论"
                )

            replies = c.get("replies")
            if not replies:
                logger.info(
                    f"动态 {target_oid_for_api} 第 {page} 页无评论，或已获取完毕"
                )
                break

            api_comment_count += len(replies)
            logger.info(
                f"API page {page}: 获取到 {len(replies)} 条根评论 (累计 {api_comment_count} / 总计 {total_api_comments})"
            )

            for cmt in replies:
                rpid_str = str(cmt.get("rpid"))
                if rpid_str and rpid_str not in processed_rpids:  # Corrected logic
                    pub_timestamp = cmt.get("ctime")
                    pub_datetime = (
                        datetime.datetime.fromtimestamp(pub_timestamp)
                        if pub_timestamp
                        else None
                    )

                    comment_data = {
                        "up_uid": mid,
                        "up_name": char_zh,
                        "oid": target_oid_for_db,
                        "rpid": rpid_str,
                        "from_dynamic": dynamic_content,
                        "mid": str(cmt.get("member", {}).get("mid")),
                        "uname": cmt.get("member", {}).get("uname"),
                        "face": cmt.get("member", {}).get("avatar"),
                        "timestamp": pub_timestamp,
                        "datetime": pub_datetime,
                        "like_num": cmt.get("like", 0),
                        "comment": cmt.get("content", {})
                        .get("message", "")
                        .replace("\n", " "),
                        "rcount": cmt.get("rcount", 0),
                        "parent_rpid": "0",
                        "is_sub_comment": False,
                        "heat": U.safe_int(cmt.get("like", 0))
                        + U.safe_int(cmt.get("rcount", 0)),
                        "sentiment": None,
                    }
                    comments_to_insert.append(comment_data)
                    processed_rpids.add(rpid_str)

                    # 获取子评论 (楼中楼)
                    if cmt.get("rcount", 0) > 0:
                        sub_cmt_api = comment.Comment(
                            oid=comment_id,
                            type_=comment_type,
                            rpid=cmt["rpid"],
                            credential=credential,
                        )
                        sub_page = 1
                        sub_comment_count = 0
                        total_sub_comments = cmt["rcount"]

                        while True:
                            try:
                                logger.info(
                                    f"获取子评论: 根 rpid {rpid_str}, sub_page: {sub_comment_count} / {total_sub_comments}"
                                )
                                rcoms = await sub_cmt_api.get_sub_comments(sub_page)
                                await asyncio.sleep(2)  # API rate limit

                                sub_replies = rcoms.get("replies")
                                if not sub_replies:
                                    logger.debug(
                                        f"根 rpid {rpid_str} 第 {sub_page} 页无子评论"
                                    )
                                    break

                                sub_comment_count += len(sub_replies)
                                logger.debug(
                                    f"子评论 page {sub_page}: 获取到 {len(sub_replies)} 条 (累计 {sub_comment_count} / 总计 {total_sub_comments})"
                                )

                                for rcom in sub_replies:
                                    sub_rpid_str = str(rcom.get("rpid"))
                                    if (
                                        sub_rpid_str
                                        and sub_rpid_str not in processed_rpids
                                    ):  # Corrected logic
                                        sub_pub_timestamp = rcom.get("ctime")
                                        sub_pub_datetime = (
                                            datetime.datetime.fromtimestamp(
                                                sub_pub_timestamp
                                            )
                                            if sub_pub_timestamp
                                            else None
                                        )

                                        sub_comment_data = {
                                            "up_uid": mid,
                                            "up_name": char_zh,
                                            "oid": target_oid_for_db,
                                            "rpid": sub_rpid_str,
                                            "from_dynamic": dynamic_content,
                                            "mid": str(
                                                rcom.get("member", {}).get("mid")
                                            ),
                                            "uname": rcom.get("member", {}).get(
                                                "uname"
                                            ),
                                            "face": rcom.get("member", {}).get(
                                                "avatar"
                                            ),
                                            "timestamp": sub_pub_timestamp,
                                            "datetime": sub_pub_datetime,
                                            "like_num": rcom.get("like", 0),
                                            "comment": rcom.get("content", {})
                                            .get("message", "")
                                            .replace("\n", " "),
                                            "rcount": 0,  # Sub-comments don't have rcount in the same way
                                            "parent_rpid": rpid_str,  # Link to parent comment
                                            "is_sub_comment": True,
                                            "heat": U.safe_int(rcom.get("like", 0))
                                            + U.safe_int(rcom.get("rcount", 0)),
                                            "sentiment": None,
                                        }
                                        comments_to_insert.append(sub_comment_data)
                                        processed_rpids.add(sub_rpid_str)

                                if sub_comment_count >= total_sub_comments:
                                    break  # Got all expected sub-comments

                                sub_page += 1
                                if sub_page > 50:  # Limit sub-pages
                                    logger.warning(
                                        f"子评论获取超过 50 页，停止获取 rpid {rpid_str} 的子评论"
                                    )
                                    break

                            except Exception as sub_ex:
                                logger.error(
                                    f"获取 rpid {rpid_str} 的子评论 page {sub_page} 时出错: {sub_ex}"
                                )
                                break

            # 检查是否需要获取下一页根评论
            if api_comment_count >= total_api_comments and total_api_comments > 0:
                logger.info(f"已获取所有 {total_api_comments} 条根评论")
                break

            page += 1
            if page > 250:  # Limit pages
                logger.warning("动态评论获取超过 250 页，停止获取")
                break

        except Exception as ex:
            logger.error(
                f"获取动态 {target_oid_for_api} 的评论 page {page} 时出错: {ex}"
            )
            break

    logger.info(f"动态 {oid} 的评论获取完成")
    return comments_to_insert


class FetchComment:

    def __init__(self, char="xingtong"):
        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)

    def set_char(self, char):
        self.char_zh = U.get_zh_role_name(char)

    async def get_aid(self, bvid: str) -> int:
        v = video.Video(bvid=bvid)
        info = await v.get_info()
        return info["aid"]

    async def get_comment_text(self, aid: str, type_num=1, split="video"):
        # https://github.com/linyuye/Bilibili_crawler/blob/main/Bilibili_crawler.py
        target_file_path = f"{self.output_dir_path}/{split}/{aid}.csv"
        res = []
        if os.path.exists(target_file_path):
            with open(
                target_file_path, mode="r", newline="", encoding="utf-8-sig"
            ) as file:
                res = list(csv.reader(file))[1:]
        else:
            credential = Credential(
                sessdata=get_sessdata("user"),
                bili_jct=get_bili_jct("user"),
                buvid3=get_buvid3("user"),
                dedeuserid=get_dedeuserid("user"),
            )
            with open(
                target_file_path, mode="w", newline="", encoding="utf-8-sig"
            ) as file:
                writer = csv.writer(file)
                writer.writerow(
                    [
                        "昵称",
                        "评论",
                        "本楼层数",
                        "时间",
                        "点赞数",
                        "mid",
                        "rpid",
                        "face",
                    ]
                )

            comments = []
            page = 1
            count = 0

            if split == "video" and aid[:3] == "BV1":
                aid = U.bv2av(aid)
            type = comment.CommentResourceType(type_num)

            while True:
                c = await comment.get_comments(
                    aid, type, page, comment.OrderType.LIKE, credential=credential
                )
                logger.info(
                    f"Fetch comment: page: {page}, count: {count}, total: {c['page']['count']}"
                )
                replies = c["replies"]

                if replies is None:
                    break

                comments.extend(replies)

                count += c["page"]["size"]
                page += 1

                if page >= 250:
                    break

                if count >= c["page"]["count"]:
                    break

                time.sleep(5)

            for cmt in comments:
                with open(
                    target_file_path, mode="a", newline="", encoding="utf-8-sig"
                ) as file:
                    info = [
                        cmt["member"]["uname"],
                        cmt["content"]["message"].replace("\n", " "),
                        cmt["rcount"],
                        datetime.datetime.fromtimestamp(cmt["ctime"]).strftime(
                            "%Y-%m-%d %H:%M:%S"
                        ),
                        cmt["like"],
                        str(cmt["member"]["mid"]),
                        str(cmt["rpid"]),
                        cmt["member"]["avatar"],
                    ]
                    res.append(info)
                    writer = csv.writer(file)
                    writer.writerows([info])

                if cmt["rcount"] > 0:
                    sub_cmt = comment.Comment(
                        oid=aid,
                        type_=type,
                        rpid=cmt["rpid"],
                        credential=credential,
                    )

                    # sub
                    for sub_page in range(1, math.ceil(cmt["rcount"] / 10) + 1):
                        try:
                            rcoms = await sub_cmt.get_sub_comments(sub_page)
                            logger.info(
                                f"Fetch comment: page: {sub_page}: in ner: {cmt['rcount']}"
                            )

                            time.sleep(3)
                        except BaseException as ex:
                            logger.error(ex)
                            break
                        if rcoms.get("replies") is not None:
                            if rcoms["replies"] == []:
                                break
                            for rcom in rcoms["replies"]:
                                with open(
                                    target_file_path,
                                    mode="a",
                                    newline="",
                                    encoding="utf-8-sig",
                                ) as file:
                                    info = [
                                        rcom["member"]["uname"],
                                        rcom["content"]["message"].replace("\n", " "),
                                        cmt["rcount"],
                                        datetime.datetime.fromtimestamp(
                                            rcom["ctime"]
                                        ).strftime("%Y-%m-%d %H:%M:%S"),
                                        rcom["like"],
                                        str(rcom["member"]["mid"]),
                                        str(rcom["rpid"]),
                                        cmt["member"]["avatar"],
                                    ]
                                    res.append(info)
                                    writer = csv.writer(file)
                                    writer.writerows([info])
                        else:
                            break

            logger.info(f"共有 {count} 条评论（不含子评论）")

        return res


if __name__ == "__main__":
    fc = FetchComment()
    a = sync(fc.get_comment_text("BV1xrStYpEVo"))
