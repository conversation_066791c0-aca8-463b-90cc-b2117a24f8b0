from pathlib import Path
import vups.utils as U


def load_prompt(prompt):
    prompts_dir = Path(__file__).parent
    prompt_path = prompts_dir / f"{prompt}.txt"
    try:
        return U.load_text(str(prompt_path))
    except Exception as e:
        print(f"Warning: Failed to load prompt {prompt}: {e}")
        return ""


def get_text_from_data(data):
    if "text" in data:
        return data["text"]
    elif "enc_text" in data:
        return U.base64_to_string(data["enc_text"])
    else:
        print("warning! failed to get text from data ", data)
        return ""


def extract_text_vec_from_datas(datas, embed_name):
    # extract text and vec from huggingface dataset
    # return texts, vecs

    texts = []
    vecs = []
    for data in datas:
        if data[embed_name] == "system_prompt":
            system_prompt = get_text_from_data(data)
        elif data[embed_name] == "config":
            pass
        else:
            vec = U.base64_to_float_array(data[embed_name])
            text = get_text_from_data(data)
            vecs.append(vec)
            texts.append(text)
    return texts, vecs, system_prompt
