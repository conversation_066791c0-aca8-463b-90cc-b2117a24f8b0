import asyncio
import time
from typing import List

import aiotieba as tb
from vups.logger import logger
from vups_server.config.const import BDUSS

# async def main():
#     async with tb.Client() as client:
#         print(await client.get_posts(9251386291, with_comments=True))

# asyncio.run(main())

## @ https://aiotieba.cc/ref/client/
# get_threads: 获取首页帖子
# get_posts: 获取主题帖中的回复
# get_comments: 获取回复中的回复


async def tieba_crawler_thread(fname: str, total_pages: int = 32):
    """
    获取贴吧名为fname的贴吧的前total_pages页中浏览量最高的10个主题帖

    Args:
        fname (str): 贴吧名
    """

    start_time = time.perf_counter()
    logger.info("Spider start")

    thread_list: List[tb.Thread] = []

    async with tb.Client(BDUSS) as client:
        task_queue = asyncio.Queue(maxsize=8)
        is_running = True

        async def producer():
            """
            生产者协程
            """

            for pn in range(total_pages, 0, -1):
                await task_queue.put(pn)
            nonlocal is_running
            is_running = False

        async def worker(i: int):
            """
            消费者协程

            Args:
                i (int): 协程编号
            """

            while 1:
                try:
                    pn = await asyncio.wait_for(task_queue.get(), timeout=1)
                except asyncio.TimeoutError:
                    if is_running is False:
                        return
                else:
                    threads = await client.get_threads(fname, pn)
                    nonlocal thread_list
                    thread_list += threads

        workers = [worker(i) for i in range(8)]
        await asyncio.gather(*workers, producer())

    logger.info(
        f"Spider complete. Time cost: {time.perf_counter()-start_time:.4f} secs"
    )

    return thread_list
    # thread_list.sort(key=lambda thread: thread.view_num, reverse=True)


# asyncio.run(tieba_crawler("星瞳official"))
