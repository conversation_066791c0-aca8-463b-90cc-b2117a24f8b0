import asyncio

from bilibili_api import video, Credential, HEADERS, get_client
import os
from vups.config import VUPS_PROJECT_ROOT
from vups_server.base.cookie_manager import get_cookie_field

SESSDATA = get_cookie_field("user", "SESSDATA")
BILI_JCT = get_cookie_field("user", "bili_jct")
BUVID3 = get_cookie_field("user", "buvid3")

FFMPEG_PATH = VUPS_PROJECT_ROOT / "vups/assets/ffmpeg/ffmpeg.exe"


class VideoDownloader:
    def __init__(self, bvid: str, credential: Credential):
        self.bvid = bvid
        self.credential = credential
        self.video = video.Video(bvid=bvid, credential=credential)


async def download(url: str, out: str, intro: str):
    dwn_id = await get_client().download_create(url, HEADERS)
    bts = 0
    tot = get_client().download_content_length(dwn_id)
    with open(out, "wb") as file:
        while True:
            bts += file.write(await get_client().download_chunk(dwn_id))
            print(f"{intro} - {out} [{bts} / {tot}]", end="\r")
            if bts == tot:
                break
    print()


async def main():
    credential = Credential(sessdata=SESSDATA, bili_jct=BILI_JCT, buvid3=BUVID3)
    v = video.Video(bvid="BV1AV411x7Gs", credential=credential)
    download_url_data = await v.get_download_url(0)
    detecter = video.VideoDownloadURLDataDetecter(data=download_url_data)
    streams = detecter.detect_best_streams()

    if detecter.check_flv_mp4_stream() == True:
        await download(streams[0].url, "flv_temp.flv", "下载 FLV 音视频流")
        os.system(f"{FFMPEG_PATH} -i flv_temp.flv video.mp4")
        os.remove("flv_temp.flv")
    else:
        # MP4 流下载
        await download(streams[0].url, "video_temp.m4s", "下载视频流")
        await download(streams[1].url, "audio_temp.m4s", "下载音频流")
        # 混流
        os.system(
            f"{FFMPEG_PATH} -i video_temp.m4s -i audio_temp.m4s -vcodec copy -acodec copy video.mp4"
        )
        # transfor 
        # 删除临时文件
        os.remove("video_temp.m4s")
        os.remove("audio_temp.m4s")

    print("已下载为：video.mp4")


if __name__ == "__main__":
    # 主入口
    asyncio.run(main())