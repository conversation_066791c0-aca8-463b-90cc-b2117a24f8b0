{"scheduler": {"timezone": "Asia/Shanghai", "max_workers": 4, "function_delay": 5, "category_delay": 10, "error_retry_count": 3, "error_retry_delay": 30}, "data_types": {"user": {"enabled": true, "description": "User data collection (followers, dynamics, videos)", "functions": {"minute": [], "hourly": ["fetch_user_follower_num", "fetch_dahanghai_num", "fetch_user_current_stat"], "daily": ["fetch_user_dynamics", "fetch_all_video", "fetch_dahanghai_list", "fetch_followers_list", "fetch_follower_review"], "three_day": ["gen_recent_relationships_30", "gen_recent_relationships_100", "gen_comment_topics", "summarise_rise_reason"], "weekly": ["fetch_fans_medal_rank", "fetch_user_info"], "monthly": ["fetch_history_follower_and_da<PERSON>hai"], "till": ["fetch_all_dynamics_comments", "fetch_all_videos_comments", "gen_comment_sensiment", "fetch_tieba_threads", "fetch_tieba_whole"]}, "schedule": {"minute": {"interval": 1, "jitter": 30}, "hourly": {"interval": 1, "jitter": 60}, "daily": {"hour": 0, "minute": 0, "jitter": 120}, "three_day": {"day": "*/3", "hour": 0, "minute": 0, "jitter": 120}, "weekly": {"day_of_week": "mon", "hour": 3, "minute": 0, "jitter": 300}, "monthly": {"day": 1, "hour": 0, "minute": 0, "jitter": 600}, "till": {"interval": 5, "mode": "idle_check", "jitter": 30}}, "limiters": {"hourly_task_limit": 1, "daily_task_limit": 1, "long_running_task_limit": 1, "till_worker_limit": 1}}, "creator": {"enabled": true, "description": "Creator data collection (existing creator_info_server functions)", "functions": {"daily": []}, "schedule": {"daily": {"hour": 2, "minute": 30}}}, "live": {"enabled": true, "description": "Live streaming data collection (room status, danmaku)", "functions": {"continuous": ["monitor_live_rooms", "collect_danmaku_data"], "minute": ["update_live_status"]}, "schedule": {"continuous": {"mode": "continuous", "description": "持续监控直播状态", "auto_restart": true, "restart_delay": 30}, "minute": {"interval": 5, "jitter": 30, "description": "每5分钟状态检查"}}, "limiters": {"concurrent_rooms": 50, "reconnect_delay": 5, "max_reconnects": 10, "status_check_interval": 300, "health_check_timeout": 60}, "monitoring": {"enable_status_updates": true, "log_level": "INFO", "error_threshold": 5, "restart_on_error": true}}}}