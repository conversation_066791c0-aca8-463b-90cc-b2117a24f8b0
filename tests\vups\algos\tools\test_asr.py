# Alibaba Paraformer ASR - Different modes
import asyncio
import os
from vups.algos.tools.asr import AliParaformerASR, BCutASR
from vups.logger import logger

test_file = "tests/vups/data/tafei.mp3"

# ali_asr = AliParaformerASR(
#     api_key=os.getenv("DASHSCOPE_API_KEY"),
#     streaming_mode=True,
# )
# result = asyncio.run(ali_asr.transcribe(test_file, ["zh", "en"], file_format="mp3", sample_rate=32000))
# logger.info(f"AliParaformerASR result: {result}")
# BCut ASR - For Bilibili processing
bcut_asr = BCutASR()
result = asyncio.run(bcut_asr.transcribe(test_file))
logger.info(f"BCutASR result: {result}")