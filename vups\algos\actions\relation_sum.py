
from datetime import datetime
from langchain.schema import HumanMessage

from vups.algos.prompts import load_prompt
from vups.base.action.abstract_action import Action
import vups.utils as U
from vups.logger import logger
from vups_server.sql.db_pool import get_connection


class RelationshipSummarize(Action):
    def __init__(
        self,
        char: str = "xingtong",
        llm_name="claude-3-5-sonnet",
    ):
        super().__init__(llm_name, "relation_sum")
        self.token_max = 5000

        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)

    def set_char(self, char):
        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)

    def render_rel_human_message(self, docs):
        prompt = load_prompt("relation_sum")
        prompt = prompt.format(char_zh=self.char_zh, docs=docs)
        return HumanMessage(content=prompt)

    async def relation_sum(self, recent_days: int):
        start_time, end_time = U.get_date_range(recent_days)

        if isinstance(start_time, str):
            start_time = datetime.fromisoformat(start_time)
        if isinstance(end_time, str):
            end_time = datetime.fromisoformat(end_time)

        mid = U.get_mid_with_role_name(self.char_zh)

        query = """
            SELECT datetime, dynamic_content
            FROM dynamics_table
            WHERE uid = $1
            AND datetime BETWEEN $2 AND $3
            AND dynamic_content IS NOT NULL
            ORDER BY datetime
        """
        async with get_connection() as conn:
            results = await conn.fetch(query, mid, start_time, end_time)

        # Combine dynamic contents
        doc = "\n".join([row["dynamic_content"] for row in results if row["dynamic_content"]])

        # logger.info(f"****Summerize Ralationship LLM Input****: {doc}")
        human_msg = self.render_rel_human_message(doc)
        logger.debug(f"****Summerize Ralationship LLM Input****: {human_msg.content}")
        try:
            response = await self.llm.ainvoke([human_msg])
            res = response.content
            logger.info(f"****Summerize Ralationship LLM Response****: {res}")
        except Exception as e:
            logger.error(f"****Summerize Ralationship LLM Error****: {e}")
            res = "LLM可能缺少额度，请联系管理员"
        return res

    def parse_replies(self, replies: str):
        """
        Parse the LLM response and return a list of strings.
        """
        if replies == "无":
            return []
        replies = replies.replace('"', "'")
        if "\n" in replies:
            replies = replies.split("\n")
            replies = list(filter(lambda x: x != "", replies))
            return replies
        else:
            return [replies]

    async def run(self, recent_days, *args, **kwargs):
        logger.info(f"run {self.__repr__()}")
        message = await self.relation_sum(recent_days)
        res = self.parse_replies(message)
        return res

# TODO: /board/relations: fetch all relations to graph
