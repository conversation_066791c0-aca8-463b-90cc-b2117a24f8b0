import os
import json
import logging
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass, field

@dataclass
class ServerConfig:
    """
    Server package configuration
    """
    # Database configuration
    database_url: Optional[str] = None
    db_connection_factory: Optional[Callable] = None
    
    # Logging configuration
    logger: Optional[logging.Logger] = None
    log_level: str = "INFO"
    
    # Cookie configuration
    cookie_config_path: str = os.path.join(os.path.dirname(__file__), 'cookie_config.json')
    
    # Scheduler configuration
    scheduler_timezone: str = "Asia/Shanghai"
    scheduler_max_workers: int = 4
    
    # Rate limiting
    enable_rate_limiting: bool = True
    default_rate_limit: int = 10  # requests per second
    
    # External dependencies
    external_dependencies: Dict[str, Any] = field(default_factory=dict)
    
    # Package mode
    standalone_mode: bool = False  # True when used as independent package

# Global configuration instance
_global_config: Optional[ServerConfig] = None

def get_config() -> ServerConfig:
    """
    Get global server configuration
    """
    global _global_config
    if _global_config is None:
        _global_config = ServerConfig()
    return _global_config

def set_config(config: ServerConfig):
    """
    Set global server configuration
    """
    global _global_config
    _global_config = config